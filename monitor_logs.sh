#!/bin/bash

# Monitor error logs in real-time
# Usage: ./monitor_logs.sh

echo "=== Bon Voyage Error Log Monitor ==="
echo "Monitoring error logs in real-time..."
echo "Press Ctrl+C to stop"
echo ""

# Function to monitor a log file
monitor_log() {
    local logfile="$1"
    local label="$2"
    
    if [ -f "$logfile" ]; then
        echo "Monitoring $label: $logfile"
        tail -f "$logfile" | while read line; do
            echo "[$label] $line"
        done &
    else
        echo "Log file not found: $logfile"
    fi
}

# Monitor multiple log files
monitor_log "app/tmp/logs/error.log" "CakePHP Error"
monitor_log "app/tmp/logs/php-errors.log" "PHP Error"
monitor_log "app/tmp/logs/debug.log" "Debug"

# Keep the script running
wait

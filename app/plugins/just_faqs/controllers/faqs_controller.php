<?php

class FaqsController extends JustFaqsAppController {

  var $name = 'Faqs';

  public $criticalCss = 'faqs';

  function index() {

    $faqs = $this->_faqs();

    $breadcrumbs = array(array(
      'text' => "FAQs",
      'url'  => $this->here
    ));

    $this->set(compact('faqs', 'breadcrumbs'));

    $this->bodyClass = 'faqs';

  }

  /**
   * Returns all faqs
   */
  protected function _faqs() {

    $faqs = function() {
      return $this->Faq->find('all');
    };

    return $this->cacher('faqs', $faqs);
  }

  function webadmin_edit($id = null) {
    // Copy the base controller logic but fix the redirect issue
    extract($this->Faq->getInfo());

    if (!$id && empty($this->data)) {
      $this->Session->setFlash(__("Invalid FAQ.", true), 'webadmin_flash_bad');
      $this->redirect(array('action' => 'index'));
      return;
    }

    if (!empty($this->data)) {
      if ($this->Faq->save($this->data)) {
        $this->Session->setFlash(__("The FAQ has been saved", true), 'webadmin_flash_good');
        // Always redirect to index to prevent redirect loops
        $this->redirect(array('action' => 'index'));
        return;
      } else {
        $this->Session->setFlash(__("The FAQ could not be saved. Please, try again.", true), 'webadmin_flash_bad');
      }
    } else {
      $this->data = $this->Faq->read(null, $id);
    }

    $this->pageTitle = __('Edit FAQ', true);
  }

  function webadmin_add() {
    // Copy the base controller logic but fix the redirect issue
    if (!empty($this->data)) {
      $this->Faq->create();
      if ($this->Faq->save($this->data)) {
        $this->Session->setFlash(__("The FAQ has been saved", true), 'webadmin_flash_good');

        if (isset($this->params['form']['submit']) && $this->params['form']['submit'] == __('Save and Add Another', true)) {
          $this->redirect(array('action' => 'add'));
        } else {
          $this->redirect(array('action' => 'index'));
        }
        return;
      } else {
        $this->Session->setFlash(__("The FAQ could not be saved. Please, try again.", true), 'webadmin_flash_bad');
      }
    }

    $this->pageTitle = __('Add FAQ', true);
  }
}

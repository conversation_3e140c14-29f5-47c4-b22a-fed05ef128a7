<?php

class FaqsController extends JustFaqsAppController {

  var $name = 'Faqs';

  public $criticalCss = 'faqs';

  function index() {

    $faqs = $this->_faqs();

    $breadcrumbs = array(array(
      'text' => "FAQs",
      'url'  => $this->here
    ));

    $this->set(compact('faqs', 'breadcrumbs'));

    $this->bodyClass = 'faqs';

  }

  /**
   * Returns all faqs
   */
  protected function _faqs() {

    $faqs = function() {
      return $this->Faq->find('all');
    };

    return $this->cacher('faqs', $faqs);
  }


}

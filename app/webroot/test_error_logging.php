<?php
/**
 * Test script to verify error logging is working
 * Access via: https://bon-voyage.ddev.site/test_error_logging.php
 */

// Include the CakePHP bootstrap to get our error configuration
define('ROOT', dirname(dirname(__FILE__)));
define('APP_DIR', 'app');
define('CAKE_CORE_INCLUDE_PATH', ROOT . DS . 'vendor' . DS . 'cakephp' . DS . 'cakephp');
define('WEBROOT_DIR', 'webroot');
define('WWW_ROOT', dirname(__FILE__) . DS);

// Include CakePHP configuration
require_once ROOT . DS . APP_DIR . DS . 'config' . DS . 'core.php';

echo "<h1>Error Logging Test</h1>";

// Test 1: Basic error logging
echo "<h2>Test 1: Basic Error Log</h2>";
error_log("TEST: Basic error logging test - " . date('Y-m-d H:i:s'));
echo "✓ Basic error logged<br>";

// Test 2: Test with detailed information
echo "<h2>Test 2: Detailed Error Log</h2>";
$testData = array(
    'test_field' => 'test_value',
    'timestamp' => time()
);

$errorMessage = "TEST: Detailed error logging test:\n";
$errorMessage .= "URL: " . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'Unknown') . "\n";
$errorMessage .= "Method: " . (isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['REQUEST_METHOD'] : 'Unknown') . "\n";
$errorMessage .= "Time: " . date('Y-m-d H:i:s') . "\n";
$errorMessage .= "Test Data: " . print_r($testData, true) . "\n";
$errorMessage .= str_repeat('-', 80) . "\n";

error_log($errorMessage);
echo "✓ Detailed error logged<br>";

// Test 3: Test exception logging
echo "<h2>Test 3: Exception Logging</h2>";
try {
    throw new Exception("Test exception for logging");
} catch (Exception $e) {
    $errorMessage = "TEST: Exception logging test:\n";
    $errorMessage .= "Message: " . $e->getMessage() . "\n";
    $errorMessage .= "File: " . $e->getFile() . "\n";
    $errorMessage .= "Line: " . $e->getLine() . "\n";
    $errorMessage .= "Stack Trace:\n" . $e->getTraceAsString() . "\n";
    $errorMessage .= str_repeat('-', 80) . "\n";
    
    error_log($errorMessage);
    echo "✓ Exception logged<br>";
}

// Show current error log configuration
echo "<h2>Current Error Log Configuration</h2>";
echo "Error logging enabled: " . (ini_get('log_errors') ? 'Yes' : 'No') . "<br>";
echo "Error log file: " . ini_get('error_log') . "<br>";
echo "Display errors: " . (ini_get('display_errors') ? 'Yes' : 'No') . "<br>";
echo "Error reporting level: " . error_reporting() . "<br>";

// Show log file location and recent entries
echo "<h2>Recent Log Entries</h2>";
$logFile = ini_get('error_log');
if (file_exists($logFile)) {
    echo "Log file exists: $logFile<br>";
    echo "Log file size: " . filesize($logFile) . " bytes<br>";
    echo "<h3>Last 10 lines:</h3>";
    echo "<pre>";
    $lines = file($logFile);
    $lastLines = array_slice($lines, -10);
    foreach ($lastLines as $line) {
        echo htmlspecialchars($line);
    }
    echo "</pre>";
} else {
    echo "Log file does not exist: $logFile<br>";
}

echo "<p><strong>Test completed!</strong> Check the error log file for the test entries.</p>";
?>

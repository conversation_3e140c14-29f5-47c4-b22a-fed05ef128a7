<?php

class BaseController extends Controller {

  var $components = array(
      'Security',
      'History',
      'RequestHandler',
      'Filter',
      'Acl',
      'Auth',
      'Cookie'
  );

  var $helpers = array(
    'Form',
    'Html',
    'Time',
    'Javascript',
    'App',
    'Image',
    'WebAdmin',
    'Text',
    'Cookies.Cookie',
  );

  var $metaDescription = '';
  var $metaKeywords = '';
  var $bodyClass = '';

  var $user = null;

  function beforeFilter() {
    $this->_siteOrAdmin();
    $this->_auth();
  }

  function _siteOrAdmin() {
    if (Configure::read('Routing.admin')
    && isset($this->params[Configure::read('Routing.admin')])) {
      $this->layout = 'webadmin';
      Configure::write('Runtime.site_or_admin', 'admin');
    } else {
      $this->layout = Configure::read('debug') == 0 ? 'prod/default' : 'dev/default';
    }
  }

  function _auth() {
    //Configure AuthComponent
    $this->Auth->actionPath = 'controllers/';
    $this->Auth->allowedActions = array('index', 'view', 'display');
    $this->Auth->authorize = 'actions';
    $this->Auth->autoRedirect = false;
    $this->Auth->fields = array('username' => 'email', 'password' => 'password');
    $this->Auth->loginAction = array('webadmin' => true, 'plugin' => null, 'controller' => 'users', 'action' => 'login');
    $this->Auth->loginRedirect = array('webadmin' => true, 'plugin' => null, 'controller' => 'destinations', 'action' => 'index');
    $this->Auth->logoutRedirect = array('webadmin' => true, 'plugin' => null, 'controller' => 'users', 'action' => 'login');
    if ($user = $this->Auth->user()) {
      $this->{$this->modelClass}->AuthUser = $user['User'];
    }
  }

  function webadmin_index($parentId = null) {

    // For some reason when called with requestAction cake did not
    // use the model I created for a join table and defaulted to
    // AppModel instead, this is here to fix this strange behaviour
    if (is_a($this->{$this->modelClass}, 'AppModel')) {
      App::import('Model', $this->modelClass);
      $this->{$this->modelClass} = new $this->modelClass;
    }

    $this->_disabledBehaviours(array('Publishable', 'Expires'));

    extract($this->{$this->modelClass}->getInfo());

    $this->$modelClass->recursive = 0;

    $order = null;
    if ($this->$modelClass->order) {
      $order = $this->$modelClass->order;
    } elseif ($isOrderable) {
      $order = $this->$modelClass->Behaviors->Sequence->settings[$modelClass]['escaped_order_field'];
    } elseif (array_key_exists('modified', $schema)) {
      $order = $this->$modelClass->escapeField('modified') . ' DESC';
    }

    if ($isTree) {

      $this->_setBreadcrumbData($parentId);

      $$pluralVar = $this->$modelClass->children($parentId, true);

    } elseif ($isOrderable) {

      $options = array('order' => $order);
      if (isset($this->paginate[$modelClass]['conditions'])) {
        $options = array_merge($options, array('conditions' => $this->paginate[$modelClass]['conditions']));
      }

      $$pluralVar = $this->$modelClass->find('all', $options);

    } else {

      $this->paginate[$modelClass]['order'] = $order;

      $this->paginate[$modelClass]['limit'] = 10;

      $tmpLimit = null;
      if (isset($this->passedArgs['limit']) && $this->passedArgs['limit'] == 'all') {
        $tmpLimit = $this->passedArgs['limit'];
        $options = array();
        if (isset($this->paginate[$modelClass]['conditions'])) {
          $options = array('conditions' => $this->paginate[$modelClass]['conditions']);
        }
        $this->passedArgs['limit'] = $this->$modelClass->find('count', $options);
      }

      $$pluralVar = $this->paginate();

      if ($tmpLimit) {
        $this->passedArgs['limit'] = $tmpLimit;
      }

    }

    $this->set(compact($pluralVar));

    $this->pageTitle = "List $pluralHumanName";

    if ($this->RequestHandler->isAjax()) {
      $this->set('ajax', true);
    }

    $this->_setModelInfo();

  }

  function _webadmin_view($id = null) {

    $this->_disabledBehaviours(array('Publishable', 'Expires'));

    extract($this->{$this->modelClass}->getInfo());

    if (!$id) {
      $this->Session->setFlash(__("Invalid $singularHumanName.", true), 'webadmin_flash_bad');
      $this->History->back(0);
    }

    $$singularVar = $this->$modelClass->read(null, $id);

    $this->set(compact($singularVar));

  }

  function webadmin_add($parentId = null) {

    extract($this->{$this->modelClass}->getInfo());

    if (!empty($this->data)) {
      $this->$modelClass->create();
      try {
        if ($this->$modelClass->save($this->data)) {
        if (!$this->RequestHandler->isAjax()) {
          if (isset($this->forcedRedirects['webadmin_add'])) {
            $this->Session->setFlash(__($this->forcedRedirects['webadmin_add']['flash']['message'], true), $this->forcedRedirects['webadmin_add']['flash']['layout']);
            $this->redirect(array_merge($this->forcedRedirects['webadmin_add']['url'], array($this->$modelClass->getInsertID())));
          } else {
            $this->Session->setFlash(__("The $singularHumanName has been saved", true), 'webadmin_flash_good');
            if (isset($this->params['form']['submit']) && $this->params['form']['submit'] == __('Save and Add Another', true)) {
              $this->History->back(0);
            } elseif (isset($this->params['form']['submit']) && $this->params['form']['submit'] == __('Save and Go Back', true)) {
              $this->History->back();
            } else {
              $this->redirect(array('action' => 'edit', $this->$modelClass->getInsertID()));
            }
          }
        } else {
          header('X-JSON: 1');

          if (isset($this->params['named']['return'])
          && $this->params['named']['return'] == 'list') {
            //
          } else {

            $hiddenFields = $this->$modelClass->hideableFields;
            $conditions = array();
            for ($i = 0; $i < count($hiddenFields); $i++) {
              if ($i == 0) {
                $foreignKey = $hiddenFields[$i];
                $id = $this->data[$modelClass][$hiddenFields[$i]];
              } else {
                $conditions[$modelClass.'.'.$hiddenFields[$i]] = $this->data[$modelClass][$hiddenFields[$i]];
              }
            }
            App::import('Helper', 'WebAdmin');
            $WebAdmin = new WebAdminHelper();
            $content = $this->requestAction($WebAdmin->relatedContentUrl($this->viewPath, $modelClass, $foreignKey, $id, $conditions), array('return'=>'return', 'bare' => 0));

            $this->set(compact('content'));

            $this->render('/shared/json/webadmin_content');

          }
        }
        } else {
          // Log validation errors for debugging
          $validationErrors = $this->$modelClass->validationErrors;
          $errorMessage = "Validation failed for new $singularHumanName:\n";
          $errorMessage .= "Data: " . print_r($this->data, true) . "\n";
          $errorMessage .= "Validation Errors: " . print_r($validationErrors, true) . "\n";
          $errorMessage .= "URL: " . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'Unknown') . "\n";
          error_log($errorMessage);

          if ($this->RequestHandler->isAjax()) {
            header('X-JSON: 0');
          }
          $this->Session->setFlash(__("The $singularHumanName could not be saved. Please, try again.", true), 'webadmin_flash_bad');
        }
      } catch (Exception $e) {
        // Log the exception with full details
        $errorMessage = "Exception in webadmin_add for new $singularHumanName:\n";
        $errorMessage .= "Message: " . $e->getMessage() . "\n";
        $errorMessage .= "File: " . $e->getFile() . "\n";
        $errorMessage .= "Line: " . $e->getLine() . "\n";
        $errorMessage .= "Data: " . print_r($this->data, true) . "\n";
        $errorMessage .= "Stack Trace:\n" . $e->getTraceAsString() . "\n";
        $errorMessage .= "URL: " . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'Unknown') . "\n";
        error_log($errorMessage);

        // Re-throw the exception to trigger the 500 error handler
        throw $e;
      }
    } else {
      if ($isTree) {
        $this->data[$modelClass]['parent_id'] = $parentId;
      }
      $this->_setDataFromParams();
    }

    if ($isTree) {
      $this->_setBreadcrumbData($parentId, true);
    }
    $this->_setAssociatedLists();
    $this->_setEnumLists();
    $this->_setModelInfo();
    $this->_setHiddenFields();

    $this->pageTitle = __('Add ', true) . $singularHumanName;

  }

  function webadmin_edit($id = null) {

    $this->_disabledBehaviours(array('Publishable', 'Expires'));

    extract($this->{$this->modelClass}->getInfo());

    if (!$id && empty($this->data)) {
      $this->Session->setFlash(__("Invalid $singularHumanName.", true), 'webadmin_flash_bad');
      $this->History->back(0);
    }

    if (!empty($this->data)) {
      try {
        if ($this->$modelClass->save($this->data)) {
          $this->Session->setFlash(__("The $singularHumanName has been saved", true), 'webadmin_flash_good');
          if (isset($this->params['form']['submit']) && $this->params['form']['submit'] == __('Save', true)) {
            die("DEBUG: About to call History->back(0) from " . $_SERVER['REQUEST_URI'] . " with submit button: " . $this->params['form']['submit']);
            $this->History->back(0);
          } else {
            die("DEBUG: About to call History->back() from " . $_SERVER['REQUEST_URI'] . " with submit button: " . (isset($this->params['form']['submit']) ? $this->params['form']['submit'] : 'NOT SET'));
            $this->History->back();
          }
        } else {
          die("DEBUG: Save FAILED! Validation errors: " . print_r($this->$modelClass->validationErrors, true));
          // Log validation errors for debugging
          $validationErrors = $this->$modelClass->validationErrors;
          $errorMessage = "Validation failed for $singularHumanName (ID: $id):\n";
          $errorMessage .= "Data: " . print_r($this->data, true) . "\n";
          $errorMessage .= "Validation Errors: " . print_r($validationErrors, true) . "\n";
          $errorMessage .= "URL: " . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'Unknown') . "\n";
          error_log($errorMessage);

          $this->Session->setFlash(__("The $singularHumanName could not be saved. Please, try again.", true), 'webadmin_flash_bad');
        }
      } catch (Exception $e) {
        // Log the exception with full details
        $errorMessage = "Exception in webadmin_edit for $singularHumanName (ID: $id):\n";
        $errorMessage .= "Message: " . $e->getMessage() . "\n";
        $errorMessage .= "File: " . $e->getFile() . "\n";
        $errorMessage .= "Line: " . $e->getLine() . "\n";
        $errorMessage .= "Data: " . print_r($this->data, true) . "\n";
        $errorMessage .= "Stack Trace:\n" . $e->getTraceAsString() . "\n";
        $errorMessage .= "URL: " . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'Unknown') . "\n";
        error_log($errorMessage);

        // Re-throw the exception to trigger the 500 error handler
        throw $e;
      }
    } else {
      $this->data = $this->$modelClass->read(null, $id);
    }

    if ($isTree) {
      $this->_setBreadcrumbData($id, false);
    }
    $this->_setAssociatedLists();
    $this->_setEnumLists();
    $this->_setModelInfo();
    $this->_setHiddenFields();

    $this->pageTitle = __('Edit ', true) . $singularHumanName . ' - ' . $this->data[$modelClass][$displayField];

  }

  function webadmin_delete($id = null) {

    $this->_disabledBehaviours(array('Publishable', 'Expires'));

    extract($this->{$this->modelClass}->getInfo());

    if (!$this->RequestHandler->isAjax()) {

      if (!$id) {
        $this->Session->setFlash(__("Invalid $singularHumanName.", true), 'webadmin_flash_bad');
      }
      if ($this->$modelClass->del($id)) {
        $this->Session->setFlash(__("$singularHumanName deleted", true), 'webadmin_flash_good');
      }
      $this->History->back(0);

    } else {

      if (!$id || !$this->$modelClass->del($id)) {
        header('X-JSON: 0');
      } else {
        header('X-JSON: 1');
      }
      exit;

    }

  }

  function webadmin_list() {

    extract($this->{$this->modelClass}->getInfo());

    $this->$modelClass->recursive = 0;

    $items = $this->$modelClass->find('list');

    $this->set(compact('items'));

    $this->viewPath = r($this->params['controller'], 'shared', $this->viewPath);

  }

  function webadmin_save_field($id = null) {

    if (!$id) {
      $this->_adminInvalidRequest('ID not specified');
    }

    extract($this->{$this->modelClass}->getInfo());

    if (empty($this->data) || !isset($this->data[$modelClass]) || empty($this->data[$modelClass])) {
      $this->_adminInvalidRequest("Key $modelClass not present in \$this->data");
    }

    $field = key($this->data[$modelClass]);

    if (!array_key_exists($field, $schema) && !($isTree && $field == 'order')) {

      $this->_adminInvalidRequest("Field $field does not exist in model $modelClass, or $modelClass is not hierarchical and field is not 'order'");

    }

    $this->$modelClass->id = $id;

    if ($isTree && $field == 'order') {

      if (!$result = $this->$modelClass->setOrder($id, $this->data[$modelClass]['order'])) {
        $errors = $this->$modelClass->Behaviors->TreeCounterCache->errors;
      }

    } else {

      if (!$result = $this->$modelClass->saveField($field, $this->data[$modelClass][$field], true)) {
        $errors = $this->$modelClass->validationErrors;
      }

    }

    if ($result) {
      if ($this->RequestHandler->isAjax()) {
        $this->set('content', true);
      } else {
        $this->Session->setFlash(__("The $singularHumanName $field has been updated", true), 'webadmin_flash_good');
        $this->History->back(0);
      }
    } else {
      if ($this->RequestHandler->isAjax()) {
        $this->set('content', json_encode($errors));
      } else {
        $this->Session->setFlash(__("The $singularHumanName $field could not be updated. ".implode(' ', $errors), true), 'webadmin_flash_bad');
        $this->History->back(0);
      }

    }

    $this->viewPath = r($this->params['controller'], 'shared', $this->viewPath);

    $this->render('webadmin_content');

  }

  function webadmin_toggle_field($field = null, $id = null) {

    if (!$id) {
      $this->_adminInvalidRequest('ID not specified');
    }

    if (!$field) {
      $this->_adminInvalidRequest('Field not specified');
    }

    extract($this->{$this->modelClass}->getInfo());

    if (!array_key_exists($field, $schema)) {
      $this->_adminInvalidRequest("Field $field does not exist in model $modelClass");
    }

    if ($schema[$field]['type'] <> 'boolean') {
      $this->_adminInvalidRequest("Field $field is not boolean");
    }

    $this->$modelClass->id = $id;

    $escapedField = $this->$modelClass->escapeField($field);

    $result = $this->$modelClass->updateAll(array(
        $escapedField => '1 - ' . $escapedField
      ),
      array(
        $this->$modelClass->escapeField() => $id
      )
    );

    if ($result) {
      $this->$modelClass->afterSave();

      if ($this->RequestHandler->isAjax()) {
        $this->set('content', true);
      } else {
        $this->Session->setFlash(__("The $singularHumanName $field has been updated", true), 'webadmin_flash_good');
        $this->History->back(0);
      }
    } else {
      $errors = $this->$modelClass->validationErrors;
      if ($this->RequestHandler->isAjax()) {
        $this->set('content', json_encode($errors));
      } else {
        $this->Session->setFlash(__("The $singularHumanName $field could not be updated. ".implode(' ', $errors), true), 'webadmin_flash_bad');
        $this->History->back(0);
      }

    }

    $this->viewPath = r($this->params['controller'], 'shared', $this->viewPath);

    $this->render('webadmin_content');

  }

  function _adminInvalidRequest($message = 'Invalid request') {

    if ($this->RequestHandler->isAjax()) {
      $this->set('content', 404);
    } else {
      $this->Session->setFlash(__($message, true), 'webadmin_flash_bad');
      $this->History->back(0);
    }

    $this->viewPath = r($this->params['controller'], 'shared', $this->viewPath);

    $this->render('webadmin_content');

  }

  function _setModelInfo() {

    $this->set($this->{$this->modelClass}->getInfo());

  }

  function _setEnumLists() {

    if ($lists = $this->{$this->modelClass}->enumLists()) {
      $this->set($lists);
    }

  }

  function _setAssociatedLists() {

    if ($lists = $this->{$this->modelClass}->associatedLists()) {
      $this->set($lists);
    }

  }

  function _setBreadcrumbData($id = null, $new = false) {

    if ($breadcrumbData = $this->{$this->modelClass}->breadcrumbData($id, $new)) {
      $this->set($breadcrumbData);
    }

  }

  function _setDataFromParams() {

    if (!isset($this->params['named'])
    || !is_array($this->params['named'])
    || empty($this->params['named'])) {
      return;
    }

    foreach ($this->params['named'] as $field => $value) {
      $this->data[$this->modelClass][$field] = $value;
    }

  }

  function _setHiddenFields() {

    if ($hiddenFields = $this->{$this->modelClass}->hiddenFields(array_keys($this->params['named']))) {
      $this->set(compact('hiddenFields'));
    }

  }

  function _webadmin_generate_fixture_records($from = 1, $to = 10) {

    extract($this->{$this->modelClass}->getInfo());

    $this->$modelClass->recursive = 0;

    $conditions = array(
    array('id >=' => $from),
    array('id <=' => $to),
    );

    $order = $primaryKey;

    $records = $this->$modelClass->find('all', compact('conditions', 'order'));

    $out = "<pre>\tvar \$records = array(\n";

    $fields = array();

    foreach ($records as $record) {
      $out .= "\t\tarray(";
      foreach ($record[$modelClass] as $field => $value) {
        $fields[$field] = "'$field' => ";
        if (is_null($value)) {
          $fields[$field] .= 'NULL';
        } elseif (is_numeric($value)) {
          $fields[$field] .= $value;
        } else {
          $fields[$field] .= "'".addslashes($value)."'";
        }
      }
      $out .= implode(', ', $fields);
      $out .= "),\n";
    }
    $out .= "\t);</pre>";

    die($out);

  }

  function _webadmin_show_tree() {

    extract($this->{$this->modelClass}->getInfo());

    $records = $this->$modelClass->generatetreelist();

    $out = '<table>';

    foreach ($records as $recordId => $recordName) {
      $out .= '<tr><td>'.$recordId.'</td><td>'.$recordName.'</td><td>'.$this->$modelClass->childCount($recordId).'</td><td>'.$this->$modelClass->childCount($recordId, true).'</td></tr>';
    }

    $out .= '</table>';

    die($out);
  }

  function _setMeta($meta) {

    $separator = ' - ';

    $metaTags = array(
      'pageTitle' => 'meta_title',
      'metaDescription' => 'meta_description',
      'metaKeywords' => 'meta_keywords',
    );

    if (is_string($meta) && !empty($meta)) {

      foreach ($metaTags as $property => $field) {
        if (empty($this->$property)) {
            $this->$property = $meta;
        } else {
            $this->$property .= $separator . $meta;
        }
      }

    } elseif (is_array($meta)) {

      foreach ($metaTags as $property => $field) {

        if (isset($meta[$field]) && !empty($meta[$field])) {
          if (!$this->$property) {
              $this->$property = $meta[$field];
          } else {
              $this->$property = $separator . $meta[$field];
          }
        }

      }

    }

  }

  function beforeRender() {

    $metaDescription = $this->metaDescription;
    $metaKeywords = $this->metaKeywords;
    $bodyClass = $this->bodyClass;

    $this->set(compact('metaDescription', 'metaKeywords', 'bodyClass'));

  }

  /**
   * Disabled the given behaviours if they're enabled
   */
  protected function _disabledBehaviours($behaviours = array()) {
    foreach ($behaviours as $value) {
      if ($this->{$this->modelClass}->Behaviors->enabled($value)) {
        $this->{$this->modelClass}->Behaviors->disable($value);
      }
    }
  }

}

?>

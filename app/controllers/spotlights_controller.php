<?php

class SpotlightsController extends AppController {

  var $name = 'Spotlights';

  var $components = array('Navigation', 'Section');

  public $criticalCss = 'hot';

  function beforeRender() {
    $this->selectedPrimaryNav = 'spotlights';
    $this->set('section', 'spotlights');
    parent::beforeRender();
  }

  function index() {

    $spotlights = $this->_findSpotlights();

    $navigation = $this->_getNavigation();

    $this->pageTitle = "What's Hot";

    $breadcrumbs = array(array(
      'text' => "What's Hot",
      'url'  => $this->here
    ));

    $heroBannerImage = RESOURCES_HOSTNAME . "/img/site/placeholders/whats-hot.jpg";

    $this->set(compact('spotlights', 'navigation', 'breadcrumbs', 'heroBannerImage'));
  }

  function view() {

    $spotlight = $this->_findBySlug($this->params['spotlight_slug']);

    if (empty($spotlight)) {
      $this->cakeError('error404');
    }

    if (!empty($spotlight['BannerImage']['id'])) {
      $heroBannerImage = $spotlight['BannerImage'];
    } else {
      $heroBannerImage = RESOURCES_HOSTNAME . "/img/site/placeholders/whats-hot.jpg";
    }

    $breadcrumbs = array(
      array(
        'text' => "What's Hot",
        'url'  => Router::url(array(
          'controller' => 'spotlights',
          'action'     => 'index'
        ))
      ),
      array(
        'text' => $spotlight['Spotlight']['name'],
        'url'  => $this->here
      )
    );

    $this->set(compact('spotlight', 'heroBannerImage', 'breadcrumbs'));

    $this->_setMeta($spotlight['Spotlight']);

  }

  /**
   * Return the full list of holiday types
   */
  protected function _findSpotlights() {

    $findSpotlights = function() {
      return $this->Spotlight->find('all');
    };

    return $this->cacher('spotlights', $findSpotlights);
  }

  /**
   * Return a spotlight from the url slug
   */
  protected function _findBySlug($slug) {

    $getBySlug = function() use ($slug) {
      return $this->Spotlight->getBySlug($slug);
    };

    return $this->cacher('spotlights_' . $slug, $getBySlug);
  }

  /**
   * Return the spotlights navigation
   */
  protected function _getNavigation($id = null) {

    $getNavigation = function() use ($id) {
      return $this->Spotlight->getNavigation($id);
    };

    return $this->cacher('spotlights_navigation', $getNavigation);
  }

  function webadmin_edit($id = null) {
    error_log("[Navigation] Starting spotlight edit for ID: " . $id);

    // Clear cache if we have data (before calling parent to avoid redirect issues)
    if ($this->data) {
        error_log("[Navigation] Clearing cache due to spotlight update");
        Cache::delete('main_navigation', 'navigation');
    }

    parent::webadmin_edit($id);
  }
}

?>

<?php
if (!class_exists('ErrorHandler')) {
    include(CORE_PATH . 'cake' . DS . 'libs' . DS . 'error.php');
}
class AppError extends ErrorHandler {

  function error404($params) {
    $this->controller->viewVars = array(
      'appError'         => true,
      'modifierCssClass' => 'four-zero-four',
      'hideBreadcrumb'   => true
    );

    $this->controller->layout = 'prod/default';

    extract($params, EXTR_OVERWRITE);

    header("HTTP/1.0 404 Not Found");
    $this->error(array('code' => '404', 'name' => 'You seem a little lost', 'message' => 'Lost'));

    $this->_stop();
  }

  function error500($params) {
    // Log detailed error information
    $logMessage = "500 Error occurred:\n";
    $logMessage .= "URL: " . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'Unknown') . "\n";
    $logMessage .= "Method: " . (isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['REQUEST_METHOD'] : 'Unknown') . "\n";
    $logMessage .= "Time: " . date('Y-m-d H:i:s') . "\n";

    if (isset($params['message'])) {
      $logMessage .= "Message: " . $params['message'] . "\n";
    }

    if (isset($params['file'])) {
      $logMessage .= "File: " . $params['file'] . "\n";
    }

    if (isset($params['line'])) {
      $logMessage .= "Line: " . $params['line'] . "\n";
    }

    if (isset($params['trace'])) {
      $logMessage .= "Stack Trace:\n" . $params['trace'] . "\n";
    }

    $logMessage .= "POST Data: " . print_r($_POST, true) . "\n";
    $logMessage .= "GET Data: " . print_r($_GET, true) . "\n";
    $logMessage .= str_repeat('-', 80) . "\n";

    // Write to error log
    error_log($logMessage);

    // Also write to CakePHP log
    if (class_exists('CakeLog')) {
      CakeLog::write('error', $logMessage);
    }

    // Set up error page
    $this->controller->viewVars = array(
      'appError'         => true,
      'modifierCssClass' => 'five-zero-zero',
      'hideBreadcrumb'   => true
    );

    $this->controller->layout = 'prod/default';

    extract($params, EXTR_OVERWRITE);

    header("HTTP/1.0 500 Internal Server Error");

    // Show detailed error in development mode
    if (Configure::read('debug') > 0) {
      $errorDetails = array(
        'code' => '500',
        'name' => 'Internal Server Error',
        'message' => isset($params['message']) ? $params['message'] : 'An internal server error occurred'
      );

      if (isset($params['file']) && isset($params['line'])) {
        $errorDetails['message'] .= ' in ' . $params['file'] . ' on line ' . $params['line'];
      }

      $this->error($errorDetails);
    } else {
      $this->error(array('code' => '500', 'name' => 'Internal Server Error', 'message' => 'Something went wrong'));
    }

    $this->_stop();
  }

}
